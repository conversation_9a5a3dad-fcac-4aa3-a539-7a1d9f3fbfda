import React, { Suspense, useEffect, useState } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, Environment, Float, Text3D, Center } from '@react-three/drei'
import { motion } from 'framer-motion'
import * as THREE from 'three'

// Particle system component
const Particles = () => {
  const [particles, setParticles] = useState([])

  useEffect(() => {
    const newParticles = []
    for (let i = 0; i < 50; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        delay: Math.random() * 6
      })
    }
    setParticles(newParticles)
  }, [])

  return (
    <div className="particles">
      {particles.map(particle => (
        <div
          key={particle.id}
          className="particle"
          style={{
            left: `${particle.x}%`,
            animationDelay: `${particle.delay}s`
          }}
        />
      ))}
    </div>
  )
}

// 3D Model Component 1 - Holographic Cube
const HolographicCube = () => {
  return (
    <Float speed={2} rotationIntensity={1} floatIntensity={2}>
      <mesh>
        <boxGeometry args={[2, 2, 2]} />
        <meshStandardMaterial
          color="#00ffff"
          transparent
          opacity={0.7}
          wireframe
          emissive="#00ffff"
          emissiveIntensity={0.2}
        />
      </mesh>
    </Float>
  )
}

// 3D Model Component 2 - Futuristic Torus
const FuturisticTorus = () => {
  return (
    <Float speed={1.5} rotationIntensity={2} floatIntensity={1}>
      <mesh rotation={[Math.PI / 4, 0, 0]}>
        <torusGeometry args={[1.5, 0.5, 16, 100]} />
        <meshStandardMaterial
          color="#ff00ff"
          transparent
          opacity={0.8}
          emissive="#ff00ff"
          emissiveIntensity={0.3}
        />
      </mesh>
    </Float>
  )
}

// 3D Model Component 3 - Holographic Sphere
const HolographicSphere = () => {
  return (
    <Float speed={3} rotationIntensity={1.5} floatIntensity={3}>
      <mesh>
        <sphereGeometry args={[1.2, 32, 32]} />
        <meshStandardMaterial
          color="#ffff00"
          transparent
          opacity={0.6}
          wireframe
          emissive="#ffff00"
          emissiveIntensity={0.4}
        />
      </mesh>
    </Float>
  )
}

// 3D Scene Component
const Scene3D = ({ modelType }) => {
  const renderModel = () => {
    switch (modelType) {
      case 'cube':
        return <HolographicCube />
      case 'torus':
        return <FuturisticTorus />
      case 'sphere':
        return <HolographicSphere />
      default:
        return <HolographicCube />
    }
  }

  return (
    <>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} intensity={1} color="#00ffff" />
      <pointLight position={[-10, -10, -10]} intensity={0.5} color="#ff00ff" />
      {renderModel()}
      <OrbitControls enableZoom={false} autoRotate autoRotateSpeed={2} />
      <Environment preset="night" />
    </>
  )
}

// Header Component
const Header = () => {
  return (
    <motion.header
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 1 }}
      className="fixed top-0 left-0 right-0 z-50 p-6"
    >
      <div className="flex justify-between items-center">
        <div className="chrono-title text-4xl holographic">
          CHRONO
        </div>
        <nav className="hidden md:flex space-x-8">
          <a href="#home" className="futuristic-text hover:neon-blue transition-all duration-300">Home</a>
          <a href="#about" className="futuristic-text hover:neon-blue transition-all duration-300">About</a>
          <a href="#models" className="futuristic-text hover:neon-blue transition-all duration-300">Models</a>
          <a href="#contact" className="futuristic-text hover:neon-blue transition-all duration-300">Contact</a>
        </nav>
      </div>
    </motion.header>
  )
}

// Hero Section Component
const HeroSection = () => {
  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative gradient-bg">
      <Particles />
      <div className="container mx-auto px-6 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1.5 }}
          className="mb-8"
        >
          <h1 className="chrono-title text-8xl md:text-9xl mb-6 glitch" data-text="CHRONO">
            CHRONO
          </h1>
          <div className="w-32 h-1 bg-gradient-to-r from-cyan-400 to-purple-500 mx-auto mb-8"></div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          <p className="futuristic-text text-xl md:text-2xl mb-8 leading-relaxed">
            AT CHRONO, WE <span className="neon-blue">TRANSCEND TIME</span> CREATING
          </p>
          <p className="futuristic-text text-xl md:text-2xl mb-8 leading-relaxed">
            FASHION THAT HARMONIZES <span className="neon-purple">TIMELESS ELEGANCE</span> WITH
          </p>
          <p className="futuristic-text text-xl md:text-2xl mb-12 leading-relaxed">
            FUTURISTIC INNOVATION, ENSURING YOU STAND OUT IN ANY ERA.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1 }}
          className="flex justify-center space-x-8"
        >
          <button className="px-8 py-3 border border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black transition-all duration-300 futuristic-text">
            EXPLORE COLLECTION
          </button>
          <button className="px-8 py-3 bg-gradient-to-r from-purple-500 to-cyan-400 text-black hover:from-cyan-400 hover:to-purple-500 transition-all duration-300 futuristic-text">
            WATCH SHOWCASE
          </button>
        </motion.div>
      </div>
    </section>
  )
}

function App() {
  return (
    <div className="min-h-screen bg-black text-white">
      <Header />
      <HeroSection />

      {/* Model Showcase Section */}
      <section id="models" className="py-20 relative">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="text-center mb-16"
          >
            <h2 className="chrono-title text-6xl mb-8 holographic">
              HOLOGRAPHIC PVC
            </h2>
            <p className="futuristic-text text-xl max-w-3xl mx-auto">
              GARMENTS <span className="neon-blue">DESIGNED</span> TO TRANSFORM YOUR WARDROBE,
              HAUTE SOPHISTICATION <span className="neon-purple">CONTEMPORARY</span>,
              ENSURING YOU STAND OUT IN ANY ERA.
            </p>
          </motion.div>

          {/* Three 3D Models Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
            {/* Model 1 */}
            <motion.div
              initial={{ opacity: 0, x: -100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 1 }}
              className="h-96 holographic-bg rounded-lg overflow-hidden"
            >
              <Canvas camera={{ position: [0, 0, 5] }}>
                <Suspense fallback={null}>
                  <Scene3D modelType="cube" />
                </Suspense>
              </Canvas>
              <div className="p-6 text-center">
                <h3 className="chrono-title text-xl mb-2 neon-blue">HOLOGRAPHIC CUBE</h3>
                <p className="futuristic-text text-sm">Geometric precision meets holographic innovation</p>
              </div>
            </motion.div>

            {/* Model 2 */}
            <motion.div
              initial={{ opacity: 0, y: 100 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.2 }}
              className="h-96 holographic-bg rounded-lg overflow-hidden"
            >
              <Canvas camera={{ position: [0, 0, 5] }}>
                <Suspense fallback={null}>
                  <Scene3D modelType="torus" />
                </Suspense>
              </Canvas>
              <div className="p-6 text-center">
                <h3 className="chrono-title text-xl mb-2 neon-purple">FUTURISTIC TORUS</h3>
                <p className="futuristic-text text-sm">Circular elegance with dimensional depth</p>
              </div>
            </motion.div>

            {/* Model 3 */}
            <motion.div
              initial={{ opacity: 0, x: 100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
              className="h-96 holographic-bg rounded-lg overflow-hidden"
            >
              <Canvas camera={{ position: [0, 0, 5] }}>
                <Suspense fallback={null}>
                  <Scene3D modelType="sphere" />
                </Suspense>
              </Canvas>
              <div className="p-6 text-center">
                <h3 className="chrono-title text-xl mb-2" style={{ color: '#ffff00', textShadow: '0 0 10px #ffff00' }}>HOLOGRAPHIC SPHERE</h3>
                <p className="futuristic-text text-sm">Perfect symmetry in holographic form</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 border-t border-gray-800">
        <div className="container mx-auto px-6 text-center">
          <div className="chrono-title text-3xl mb-6 holographic">CHRONO</div>
          <p className="futuristic-text text-gray-400 mb-6">
            Transcending time through fashion innovation
          </p>
          <div className="flex justify-center space-x-8 mb-6">
            <a href="#" className="futuristic-text hover:neon-blue transition-all duration-300">Instagram</a>
            <a href="#" className="futuristic-text hover:neon-blue transition-all duration-300">Twitter</a>
            <a href="#" className="futuristic-text hover:neon-blue transition-all duration-300">LinkedIn</a>
            <a href="#" className="futuristic-text hover:neon-blue transition-all duration-300">Contact</a>
          </div>
          <p className="futuristic-text text-sm text-gray-500">
            © 2024 CHRONO. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}

export default App
