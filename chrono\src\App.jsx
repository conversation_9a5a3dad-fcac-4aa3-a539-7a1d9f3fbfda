import React from 'react'

function App() {
  return (
    <div className="min-h-screen bg-gray-900 text-white relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black"></div>

      {/* Main content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center">
        <div className="container mx-auto px-6 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">

          {/* Left side - Text content */}
          <div className="text-center lg:text-left space-y-8 slide-in-left">
            {/* Subtitle */}
            <div className="text-gray-300 text-lg tracking-[0.3em] font-rajdhani font-light uppercase">
              Timeless Threads
            </div>

            {/* Main title */}
            <div className="space-y-4">
              <h1 className="text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-orbitron font-black tracking-[0.1em] text-chrome">
                <span className="block">CHRONO</span>
              </h1>
            </div>

            {/* Description text */}
            <div className="text-gray-400 text-base max-w-md mx-auto lg:mx-0 leading-relaxed font-rajdhani">
              Explore our collection of revolutionary<br />
              garments crafted with cutting-edge<br />
              materials and timeless design.
            </div>

            {/* Scroll indicator */}
            <div className="text-gray-500 text-xs tracking-[0.3em] font-rajdhani uppercase">
              SCROLL DOWN ↓
            </div>
          </div>

          {/* Right side - Character image */}
          <div className="flex justify-center lg:justify-end slide-in-right">
            <div className="relative">
              {/* Character image container */}
              <div className="w-80 h-[500px] relative overflow-hidden rounded-lg glow-border">
                {/* Replace this div with your actual image */}
                <img
                  src="/path-to-your-character-image.jpg"
                  alt="Futuristic Character"
                  className="w-full h-full object-cover object-center"
                  onError={(e) => {
                    // Fallback if image doesn't load
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'flex';
                  }}
                />

                {/* Fallback placeholder */}
                <div className="absolute inset-0 bg-gradient-to-b from-cyan-400/20 to-purple-500/20 flex items-center justify-center" style={{display: 'none'}}>
                  <div className="text-center text-gray-400">
                    <div className="text-4xl mb-4">�</div>
                    <div className="text-sm font-rajdhani">Character Image</div>
                    <div className="text-xs mt-2">Replace src with your image path</div>
                  </div>
                </div>

                {/* Holographic overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-transparent via-cyan-400/5 to-purple-500/10 pointer-events-none"></div>
              </div>

              {/* Glow effects */}
              <div className="absolute inset-0 bg-gradient-to-b from-cyan-400/10 to-purple-500/10 rounded-lg blur-xl -z-10"></div>
              <div className="absolute -inset-2 bg-gradient-to-r from-cyan-400/20 to-purple-500/20 rounded-lg blur-2xl -z-20 opacity-50"></div>
            </div>
          </div>

        </div>
      </div>

      {/* Subtle grid pattern overlay */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}></div>
      </div>
    </div>
  )
}

export default App
